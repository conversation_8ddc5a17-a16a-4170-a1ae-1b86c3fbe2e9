import re
import time
import random
from datetime import datetime

from schema import EmailMessage


def parse_spamc_output(spamc_output: str) -> dict:
    """
    Parse comprehensive spamc output into structured data
    :param spamc_output: Raw output string from spamc command
    """
    result = {
        'headers': {},
        'spam_info': {},
        'content_analysis': [],
        'original_message': '',
        'raw_output': spamc_output
    }
    
    lines = spamc_output.split('\n')
    
    # Parse spam headers
    for line in lines:
        if line.startswith('X-Spam-'):
            if ':' in line:
                key, value = line.split(':', 1)
                result['headers'][key.strip()] = value.strip()
    
    # Extract key spam information
    spam_status_match = re.search(r'X-Spam-Status:\s*(\w+),\s*score=([\d.-]+)\s*required=([\d.-]+)\s*tests=([A-Z_,\s]+)', spamc_output, re.MULTILINE)
    if spam_status_match:
        result['spam_info'] = {
            'status': spam_status_match.group(1),
            'score': float(spam_status_match.group(2)),
            'spamassassin_threshold': float(spam_status_match.group(3)),
            'tests': [test.strip() for test in spam_status_match.group(4).replace('\n\t', '').split(',') if test.strip()],
            'is_spam': spam_status_match.group(1).lower() == 'yes'
        }
    
    # Parse content analysis details (the detailed scoring table)
    analysis_match = re.search(r'Content analysis details:.*?\n\n pts rule name.*?\n---- .*? ----.*?\n(.*?)(?=\n\n|\n------------)', spamc_output, re.DOTALL)
    if analysis_match:
        analysis_lines = analysis_match.group(1).strip().split('\n')
        for line in analysis_lines:
            if line.strip():
                # Parse each rule line: score, rule_name, description
                rule_match = re.match(r'\s*([\d.-]+)\s+([A-Z_]+)\s+(.*)', line)
                if rule_match:
                    result['content_analysis'].append({
                        'points': float(rule_match.group(1)),
                        'rule_name': rule_match.group(2),
                        'description': rule_match.group(3).strip()
                    })
    
    # Extract original message
    original_match = re.search(r'Content-Transfer-Encoding: 8bit\n\n(.*)', spamc_output, re.DOTALL)
    if original_match:
        result['original_message'] = original_match.group(1).strip()
    
    return result


def build_email_with_headers(email: EmailMessage) -> str:
    """
    Build a properly formatted email with headers to prevent MISSING_* spam rules.
    :param email: EmailMessage object containing content and optional headers
    """
    headers = []
    
    # Add Date header (required to prevent MISSING_DATE)
    if email.date:
        headers.append(f"Date: {email.date}")
    else:
        # Generate current date in RFC 2822 format
        current_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S %z")
        if not current_date.endswith((' +0000', ' -0000')):  # Add timezone if missing
            current_date += " +0000"
        headers.append(f"Date: {current_date}")
    
    # Add From header (required to prevent MISSING_FROM)
    if email.from_email:
        headers.append(f"From: {email.from_email}")
    else:
        headers.append("From: <EMAIL>")
    
    # Add To header (required to prevent MISSING_HEADERS)
    if email.to_email:
        headers.append(f"To: {email.to_email}")
    else:
        headers.append("To: <EMAIL>")
    
    # Add Subject header (required to prevent MISSING_SUBJECT)
    if email.subject:
        headers.append(f"Subject: {email.subject}")
    else:
        headers.append("Subject: No Subject")
    
    # Add Message-ID header (required to prevent MISSING_MID)
    if email.message_id:
        headers.append(f"Message-ID: {email.message_id}")
    else:
        # Generate a basic message ID
        msg_id = f"<{int(time.time())}.{random.randint(1000, 9999)}@example.com>"
        headers.append(f"Message-ID: {msg_id}")
    
    # Add optional headers if provided
    if email.reply_to:
        headers.append(f"Reply-To: {email.reply_to}")
    
    if email.cc:
        headers.append(f"Cc: {email.cc}")
    
    if email.bcc:
        headers.append(f"Bcc: {email.bcc}")
    
    # Add MIME version to prevent some MIME-related spam rules
    headers.append("MIME-Version: 1.0")
    headers.append("Content-Type: text/plain; charset=utf-8")
    headers.append("Content-Transfer-Encoding: 8bit")
    
    # Combine headers with content (blank line separates headers from body)
    email_with_headers = "\n".join(headers) + "\n\n" + email.content.strip()
    
    return email_with_headers
